import React, { useState } from 'react';
import { WifiIcon } from '@heroicons/react/24/outline';
import WifiModalReact from './WifiModalReact';

export default function WifiButton() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return (
    <>
      {/* WiFi Icon Button */}
      <button
        onClick={openModal}
        aria-label="WiFi Information"
        className="group flex justify-center items-center bg-white/10 hover:bg-white/20 p-2 sm:p-3 rounded-lg transition-colors nav-icon-link"
      >
        <WifiIcon className="w-4 sm:w-5 h-4 sm:h-5 text-white group-hover:scale-110 transition-transform nav-icon" />
      </button>

      {/* WiFi Modal */}
      <WifiModalReact isOpen={isModalOpen} onClose={closeModal} />
    </>
  );
}
