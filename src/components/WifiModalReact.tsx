import React, { useState, useRef, useEffect } from 'react';
import { Dialog, DialogPanel, DialogTitle } from '@headlessui/react';
import { XMarkIcon, WifiIcon, ClipboardIcon, CheckIcon } from '@heroicons/react/24/outline';

interface WifiModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function WifiModalReact({ isOpen, onClose }: WifiModalProps) {
  const [copied, setCopied] = useState(false);
  const [dragY, setDragY] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const sheetRef = useRef<HTMLDivElement>(null);
  const startY = useRef(0);
  const currentY = useRef(0);

  const networkName = 'Beach Bar Joni';
  const password = 'golem2025';

  // Reset drag state when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setDragY(0);
      setIsDragging(false);
    }
  }, [isOpen]);

  // Global mouse and touch event listeners for drag handling
  useEffect(() => {
    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;

      currentY.current = e.clientY;
      const deltaY = currentY.current - startY.current;

      if (deltaY > 0) {
        setDragY(deltaY);
      }
    };

    const handleGlobalMouseUp = () => {
      if (!isDragging) return;

      setIsDragging(false);
      const deltaY = currentY.current - startY.current;

      if (deltaY > 100) {
        onClose();
      } else {
        setDragY(0);
      }
    };

    const handleGlobalTouchMove = (e: TouchEvent) => {
      if (!isDragging) return;
      console.log('Global touch move');

      currentY.current = e.touches[0].clientY;
      const deltaY = currentY.current - startY.current;

      if (deltaY > 0) {
        setDragY(deltaY);
        e.preventDefault();
      }
    };

    const handleGlobalTouchEnd = () => {
      if (!isDragging) return;
      console.log('Global touch end');

      setIsDragging(false);
      const deltaY = currentY.current - startY.current;

      if (deltaY > 100) {
        onClose();
      } else {
        setDragY(0);
      }
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);
      document.addEventListener('touchmove', handleGlobalTouchMove, { passive: false });
      document.addEventListener('touchend', handleGlobalTouchEnd);
    }

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
      document.removeEventListener('touchmove', handleGlobalTouchMove);
      document.removeEventListener('touchend', handleGlobalTouchEnd);
    };
  }, [isDragging, onClose]);

  const copyPassword = async () => {
    try {
      await navigator.clipboard.writeText(password);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = password;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  // Drag handling functions
  const handleTouchStart = (e: React.TouchEvent) => {
    console.log('Touch start detected');
    setIsDragging(true);
    startY.current = e.touches[0].clientY;
    currentY.current = e.touches[0].clientY;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return;
    console.log('Touch move detected, dragging:', isDragging);

    currentY.current = e.touches[0].clientY;
    const deltaY = currentY.current - startY.current;
    console.log('Delta Y:', deltaY);

    // Only allow dragging down (positive deltaY)
    if (deltaY > 0) {
      setDragY(deltaY);
      // Prevent scrolling when dragging
      e.preventDefault();
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (!isDragging) return;
    console.log('Touch end detected');

    setIsDragging(false);
    const deltaY = currentY.current - startY.current;
    console.log('Final delta Y:', deltaY);

    // Close if dragged down more than 100px
    if (deltaY > 100) {
      console.log('Closing modal');
      onClose();
    } else {
      console.log('Snapping back');
      // Snap back to original position
      setDragY(0);
    }
  };

  // Mouse events for desktop testing
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
    startY.current = e.clientY;
    currentY.current = e.clientY;
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="z-50 relative">
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" aria-hidden="true" />
      
      {/* Full-screen container */}
      <div className="fixed inset-0 flex justify-center items-center p-4">
        {/* Desktop Modal */}
        <DialogPanel className="hidden sm:block relative bg-white shadow-2xl rounded-2xl w-full max-w-sm max-h-[90vh] overflow-hidden">
          {/* Header */}
          <div className="flex justify-between items-center p-6 border-gray-200 border-b">
            <div className="flex items-center space-x-3">
              <div className="flex justify-center items-center bg-blue-100 rounded-full w-10 h-10">
                <WifiIcon className="w-5 h-5 text-blue-600" />
              </div>
              <DialogTitle className="font-semibold text-gray-900 text-lg">
                Beach Bar Joni WiFi
              </DialogTitle>
            </div>
            <button
              onClick={onClose}
              className="flex justify-center items-center hover:bg-gray-100 rounded-full w-8 h-8 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
          
          {/* Content */}
          <div className="space-y-6 p-6">
            {/* WiFi Network Info */}
            <div className="space-y-4">
              <div className="text-center">
                <div className="flex justify-center mb-4">
                  <img src="/WiFi_Logo.svg.png" alt="WiFi" className="w-16 h-auto object-contain" />
                </div>
                <h4 className="mb-2 font-bold text-gray-900 text-xl">Beach Bar Joni WiFi</h4>
              </div>
              
              {/* Network Details */}
              <div className="space-y-3 bg-gray-50 p-4 rounded-xl">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-gray-700 text-sm">Network Name:</span>
                  <span className="bg-white px-3 py-1 border rounded-lg font-mono text-sm">
                    {networkName}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium text-gray-700 text-sm">Password:</span>
                  <div className="flex items-center space-x-2">
                    <span className="bg-white px-3 py-1 border rounded-lg font-mono text-sm">
                      {password}
                    </span>
                    <button
                      onClick={copyPassword}
                      className="flex justify-center items-center hover:bg-blue-50 rounded-lg w-8 h-8 text-gray-500 hover:text-blue-600 transition-colors"
                      title="Copy password"
                    >
                      {copied ? (
                        <CheckIcon className="w-4 h-4 text-green-600" />
                      ) : (
                        <ClipboardIcon className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DialogPanel>
        
        {/* Mobile Sheet */}
        <DialogPanel
          ref={sheetRef}
          className="sm:hidden right-0 bottom-0 left-0 fixed bg-white shadow-2xl rounded-t-3xl max-h-[85vh] overflow-hidden transition-transform duration-300 ease-out"
          style={{
            transform: `translateY(${dragY}px)`,
            opacity: isDragging ? Math.max(0.5, 1 - dragY / 200) : 1
          }}
        >
          {/* Sheet Handle - Draggable */}
          <div
            className="flex justify-center pt-4 pb-4 cursor-grab active:cursor-grabbing select-none"
            style={{ touchAction: 'none' }}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            onMouseDown={handleMouseDown}
          >
            <div className="bg-gray-300 rounded-full w-12 h-1 pointer-events-none"></div>
          </div>
          
          {/* Header */}
          <div className="flex justify-between items-center px-6 py-4 border-gray-200 border-b">
            <DialogTitle className="font-semibold text-gray-900 text-lg">
              Beach Bar Joni WiFi
            </DialogTitle>
            <button
              onClick={onClose}
              className="flex justify-center items-center hover:bg-gray-100 rounded-full w-8 h-8 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
          
          {/* Content */}
          <div className="space-y-6 px-6 py-4 overflow-y-auto">
            {/* WiFi Network Info */}
            <div className="space-y-4">
              <div className="text-center">
                <div className="flex justify-center mb-4">
                  <img src="/WiFi_Logo.svg.png" alt="WiFi" className="w-16 h-auto object-contain" />
                </div>
                <h4 className="mb-2 font-bold text-gray-900 text-xl">Beach Bar Joni WiFi</h4>
              </div>
              
              {/* Network Details */}
              <div className="space-y-3 bg-gray-50 p-4 rounded-xl">
                <div className="space-y-2">
                  <span className="font-medium text-gray-700 text-sm">Network Name:</span>
                  <div className="bg-white px-3 py-2 border rounded-lg font-mono text-sm text-center">
                    {networkName}
                  </div>
                </div>
                <div className="space-y-2">
                  <span className="font-medium text-gray-700 text-sm">Password:</span>
                  <div className="flex items-center space-x-2">
                    <span className="flex-1 bg-white px-3 py-2 border rounded-lg font-mono text-sm text-center">
                      {password}
                    </span>
                    <button
                      onClick={copyPassword}
                      className="flex justify-center items-center hover:bg-blue-50 rounded-lg w-10 h-10 text-gray-500 hover:text-blue-600 transition-colors"
                      title="Copy password"
                    >
                      {copied ? (
                        <CheckIcon className="w-5 h-5 text-green-600" />
                      ) : (
                        <ClipboardIcon className="w-5 h-5" />
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
